name: Automated Tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

env:
  PHP_VERSION: '8.1'
  MYSQL_ROOT_PASSWORD: root
  MYSQL_DATABASE: kaufino
  env: test

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  codeception-tests:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mariadb:latest
        env:
          MYSQL_ROOT_PASSWORD: ${{ env.MYSQL_ROOT_PASSWORD }}
          MYSQL_DATABASE: ${{ env.MYSQL_DATABASE }}
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mariadb-admin ping --silent"
          --health-interval=5s
          --health-timeout=3s
          --health-retries=5
          --tmpfs=/var/lib/mysql:rw,noexec,nosuid,size=2g

    strategy:
      matrix:
        test-suite: [acceptance]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP with Nette-optimized extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.PHP_VERSION }}
          extensions: zip, gd, pdo_mysql, soap, mysqli, intl, opcache, apcu, bcmath, xml, mbstring, curl
          coverage: none
          ini-values: |
            memory_limit=2G
            opcache.enable=1
            opcache.memory_consumption=256
            opcache.max_accelerated_files=20000
            opcache.validate_timestamps=0
            opcache.revalidate_freq=0
            realpath_cache_size=8M
            realpath_cache_ttl=600

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.composer/cache
            vendor
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Cache application temp directories
        uses: actions/cache@v4
        with:
          path: |
            temp
            temp_local
          key: ${{ runner.os }}-app-temp-${{ github.sha }}
          restore-keys: ${{ runner.os }}-app-temp-

      - name: Install dependencies
        run: |
          echo "Installing Composer dependencies..."
          if [ -d "vendor" ] && [ -f "vendor/autoload.php" ]; then
            echo "Vendor directory found in cache"
            echo "Cache hit: $(ls -la vendor/ | wc -l) vendor directories"
          else
            echo "Vendor directory not cached, fresh install required"
          fi
          composer install --no-progress --prefer-dist --optimize-autoloader --ignore-platform-reqs

      - name: Setup directories and permissions
        run: |
          mkdir -p temp/{sessions,cache,proxies} temp_local/{cache,sessions} www/{upload/thumbnails,webtemp,webtemp/js,webtemp/css} log sessions
          chmod -R 0777 temp temp_local log www/upload www/webtemp sessions tests
          chmod -R 0755 vendor/bin/
          sudo chown -R $USER:$USER .

      - name: Configure MariaDB for Kaufino performance
        run: |
          echo "127.0.0.1 mysql" | sudo tee -a /etc/hosts
          # MariaDB-specific commands and performance optimizations
          mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL character_set_server=utf8mb4;"
          mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL collation_server=utf8mb4_unicode_ci;"
          mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL innodb_flush_log_at_trx_commit=0;"
          mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL sync_binlog=0;"
          mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL innodb_buffer_pool_size=1073741824;" # 1GB
          mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL slow_query_log=1;"
          mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL long_query_time=2;"

      - name: Setup Nette environment for testing
        run: |
          # Create local.neon config for testing environment (based on buddy.neon structure)
          cat > app/config/local.neon << EOL
          parameters:
          	mode: development
          	database:
          		dsn: 'mysql:host=127.0.0.1:3306;dbname=kaufino'
          		driver: 'pdo_mysql'
          		host: 127.0.0.1:3306
          		dbname: kaufino
          		user: root
          		password: root
          	azure:
          		api:
          		    token: '123456'
          EOL
          
          # Setup web server for Codeception tests
          sudo apt-get update -qq
          sudo apt-get install -y nginx php8.1-fpm php8.1-zip php8.1-gd php8.1-mysql php8.1-soap php8.1-intl php8.1-mbstring php8.1-xml php8.1-curl php8.1-bcmath
          sudo mkdir -p /var/www/kaufino
          sudo cp -r . /var/www/kaufino/
          sudo mkdir -p /var/www/kaufino/temp/{sessions,cache,proxies} /var/www/kaufino/temp_local/{sessions,cache} /var/www/kaufino/sessions
          
          # Configure PHP-FPM pool for Kaufino
          sudo tee /etc/php/8.1/fpm/pool.d/kaufino.conf > /dev/null <<EOL
          [kaufino]
          user = www-data
          group = www-data
          listen = /var/run/php/php8.1-fpm-kaufino.sock
          listen.owner = www-data
          listen.group = www-data
          listen.mode = 0660
          pm = dynamic
          pm.max_children = 8
          pm.start_servers = 3
          pm.min_spare_servers = 2
          pm.max_spare_servers = 4
          pm.max_requests = 200
          php_admin_value[session.save_handler] = files
          php_admin_value[session.save_path] = /var/www/kaufino/temp_local/sessions
          php_admin_value[upload_tmp_dir] = /var/www/kaufino/temp_local
          php_admin_flag[display_errors] = on
          php_admin_value[error_reporting] = E_ALL
          php_admin_value[memory_limit] = 384M
          EOL
          
          # Configure Nginx for Kaufino with multi-domain support
          sudo tee /etc/nginx/sites-available/kaufino > /dev/null <<EOL
          server {
              listen 8080;
              server_name kaufino.dev *.dev kaufino.localhost letado.dev *.letado.dev oferto.dev *.oferto.dev;
              root /var/www/kaufino/www;
              index index.php;
          
              location ~ /\. {
                  deny all;
              }
          
              location ~* \.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz|map)$ {
                  expires 1y;
                  add_header Cache-Control "public, immutable";
                  try_files \$uri =404;
              }
          
              location / {
                  try_files \$uri \$uri/ @rewrite;
              }
          
              location @rewrite {
                  rewrite ^(.*)$ /index.php last;
              }
          
              location ~ \.php$ {
                  include fastcgi_params;
                  fastcgi_pass unix:/var/run/php/php8.1-fpm-kaufino.sock;
                  fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
                  fastcgi_param HTTP_AUTHORIZATION \$http_authorization;
                  fastcgi_read_timeout 300;
              }
          }
          EOL
          
          sudo ln -sf /etc/nginx/sites-available/kaufino /etc/nginx/sites-enabled/
          sudo rm -f /etc/nginx/sites-enabled/default
          sudo chown -R www-data:www-data /var/www/kaufino
          sudo find /var/www/kaufino -type d -exec chmod 755 {} \;
          sudo find /var/www/kaufino -type f -exec chmod 644 {} \;
          sudo chmod -R 777 /var/www/kaufino/temp /var/www/kaufino/temp_local /var/www/kaufino/log /var/www/kaufino/www/upload /var/www/kaufino/www/webtemp /var/www/kaufino/sessions
          
          # Add hosts entries for multi-domain testing
          echo "127.0.0.1 kaufino.dev" | sudo tee -a /etc/hosts
          echo "127.0.0.1 letado.dev" | sudo tee -a /etc/hosts
          echo "127.0.0.1 oferto.dev" | sudo tee -a /etc/hosts
          echo "127.0.0.1 kaufino.localhost" | sudo tee -a /etc/hosts
          
          sudo nginx -t
          sudo systemctl restart php8.1-fpm nginx

      - name: Clear Doctrine caches and proxies
        run: |
          rm -rf temp/cache/* temp_local/cache/* temp/proxies/*
          
          # Initialize Kaufino database
          mysql -h127.0.0.1 -uroot -proot -e "DROP DATABASE IF EXISTS kaufino; CREATE DATABASE kaufino CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
          
          # Import base data from adminer.sql (as used by buddy pipeline)
          mysql -h127.0.0.1 -uroot -proot kaufino < www/adminer/adminer.sql
          php bin/console orm:generate-proxies
          php -d memory_limit=2G bin/console orm:schema-tool:update --force --verbose
          php bin/console orm:validate-schema --skip-sync

      - name: Configure Codeception for Kaufino testing
        run: |
          # Update acceptance.suite.yml for Kaufino-specific setup (URL already correct)
          sed -i 's|url: .*|url: http://kaufino.dev:8080/|' tests/acceptance.suite.yml
          
          # Ensure _output directory exists
          mkdir -p tests/_output
          chmod -R 755 tests/_output
          
          # Ensure support directory exists for Gherkin fix
          mkdir -p tests/_support
          
          # Ensure test suite directories exist
          mkdir -p tests/functional tests/unit
          
          # Build Codeception with correct configuration
          php vendor/bin/codecept build -v

      - name: Run Kaufino Codeception tests
        env:
          ENVIRONMENT: test
          NETTE_DEBUG: 1
        run: |
          # Run acceptance tests with extended timeout for Nette application
          php -d memory_limit=2G vendor/bin/codecept run acceptance \
              --no-colors \
              --no-interaction \
              --fail-fast

      - name: Upload Codeception failure artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: codeception-failure-artifacts-${{ matrix.test-suite }}-${{ github.run_id }}
          path: |
            tests/_output/
            log/
            temp/log/
            temp_local/log/
          if-no-files-found: ignore

  static-analysis:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        tool: [phpstan, codesniffer]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP for Kaufino
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.PHP_VERSION }}
          extensions: zip, gd, pdo_mysql, soap, mysqli, intl, bcmath, xml, mbstring, curl
          coverage: none
          ini-values: memory_limit=3G

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.composer/cache
            vendor
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: |
          echo "Installing Composer dependencies for static analysis..."
          if [ -d "vendor" ] && [ -f "vendor/autoload.php" ]; then
            echo "Vendor directory found in cache"
            echo "Cache hit: $(ls -la vendor/ | wc -l) vendor directories"
          else
            echo "Vendor directory not cached, fresh install required"
          fi
          composer install --no-progress --prefer-dist --optimize-autoloader --ignore-platform-reqs

      - name: Create temp directories for static analysis
        run: |
          mkdir -p temp/{sessions,cache,proxies} temp_local/{cache,sessions}
          chmod -R 755 temp temp_local

      - name: Run Kaufino static analysis
        run: |
          case ${{ matrix.tool }} in
              phpstan)
                  if [ -f "vendor/bin/phpstan" ]; then
                      # Run PHPStan with Kaufino-specific configuration (level 1)
                      php -d memory_limit=4G vendor/bin/phpstan analyse \
                          -l 1 -c phpstan.neon \
                          --no-progress --error-format=table --verbose \
                          app
                  else
                      echo "PHPStan is not installed. Please run 'composer install' to install dev dependencies."
                      exit 1
                  fi
                  ;;
              codesniffer)
                  if [ -f "vendor/bin/phpcs" ]; then
                      # Run PHP_CodeSniffer with Slevomat Coding Standard
                      php vendor/bin/phpcs \
                          --standard=ruleset.xml \
                          --extensions=php \
                          --tab-width=4 \
                          --parallel=2 \
                          --report=full \
                          --colors \
                          app bin www
                  else
                      echo "PHP_CodeSniffer is not installed. Please run 'composer install' to install dev dependencies."
                      exit 1
                  fi
                  ;;
          esac

