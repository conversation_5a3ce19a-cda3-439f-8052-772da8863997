<?php
/**
 * Gherkin Loader Fix for Codeception 5.0
 * 
 * This file prevents the Gherkin loader from failing when behat/gherkin 
 * i18n.php file is missing. Since this project doesn't use .feature files,
 * we can safely disable Gherkin functionality.
 */

// Check if we're in Codeception context and the missing file error would occur
if (class_exists('Codeception\Test\Loader\Gherkin')) {
    // Create a dummy i18n.php file in memory to prevent the missing file error
    $gherkinSrcPath = dirname((new ReflectionClass('Behat\Gherkin\Parser'))->getFileName());
    $i18nPath = $gherkinSrcPath . '/../../../i18n.php';
    
    // If the file doesn't exist, create a minimal one
    if (!file_exists($i18nPath)) {
        $i18nDir = dirname($i18nPath);
        if (!is_dir($i18nDir)) {
            mkdir($i18nDir, 0755, true);
        }
        
        // Create a minimal i18n.php file that prevents the error
        file_put_contents($i18nPath, '<?php
// Minimal i18n file to prevent Codeception Gherkin loader error
// This project does not use Gherkin/BDD features
return [];
');
    }
}