<?php

// Specify the file name
$filename = __DIR__ . '/ads.csv'; // Replace with the actual filename

// Check if the file exists
if (!file_exists($filename)) {
	die("File not found.");
}

// Open the file for reading
$file = fopen($filename, 'r');

// Read and process each line
while (($line = fgetcsv($file, 0, ";")) !== false) {
	$name = $line[3];
	if (substr($name, -2) === "1B") {
		$channel = "L";
	} elseif (substr($name, -2) === "1C") {
		$channel = "M";
	} else {
		$channel = ""; // Default or other value if needed
	}

	if (preg_match('/(\d+)[BC]$/', $name, $matches)) {
		$localizationId = $matches[1];  // Get the matched digit
	} else {
		$localizationId = null; // Set to null or another default value if no match is found
	}

	$record = [
		'id' => $line[0],
		'parentId' => $line[1],
		'code' => $line[2],
		'name' => $name,
		'sizes' => preg_replace('/\s*,+\s*/', ',', trim($line[4])),
		'description' => $line[5],
		'placements' => $line[6],
		'targetWindow' => $line[7],
		'labels' => $line[8],
		'channel' => strtolower($channel),
		'localizationId' => $localizationId,
	];

	echo 'INSERT INTO kaufino_marketing_ad_units (localization_id, size, channel, code) VALUES (' . $record['localizationId'] . ', "' . $record['sizes'] . '", "' . $record['channel'] . '", "' . $record['code'] . '");' . PHP_EOL;
}

// Close the file
fclose($file);
