<?php

declare(strict_types=1);

if (file_exists(__DIR__ . '/maintenance.php')) {
	require __DIR__ . '/maintenance.php';
}

require __DIR__ . '/../vendor/autoload.php';
require __DIR__ . '/../app/Bootstrap.php';
$configurator = <PERSON><PERSON>ino\Bootstrap::boot();
$container = $configurator->createContainer();
$application = $container->getByType(Nette\Application\Application::class);
$application->run();
