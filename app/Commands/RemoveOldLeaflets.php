<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\SyncManager;

class RemoveOldLeaflets extends Job
{
    private LeafletFacade $leafletFacade;

    public function __construct(LeafletFacade $leafletFacade)
    {
        parent::__construct();

        $this->leafletFacade = $leafletFacade;
    }

    protected function configure(): void
    {
        $this->setName('kaufino:remove-old-leaflets');
    }

    public function start(): void
    {
        $log = $this->onStart();

        $this->removeOldLeaflets();

        $this->onFinish($log);
    }

    private function removeOldLeaflets(): void
    {
        $leafletsToRemove = $this->leafletFacade->findLeafletsToRemove();

        foreach ($leafletsToRemove as $leaflet) {
            $this->leafletFacade->removeLeaflet($leaflet);
        }
    }
}