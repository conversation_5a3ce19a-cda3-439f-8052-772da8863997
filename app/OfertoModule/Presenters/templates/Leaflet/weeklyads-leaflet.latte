{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}


{block title}
    {if $leaflet->isExpired()}
        {_oferto.leaflet.expiredLeafletTitle, [brand => $leaflet->getName(), validTill => $validTill]}
    {elseif $leaflet->isFuture()}
        {_oferto.leaflet.futureLeafletTitle, [brand => $leaflet->getName(), validSince => $validSince, validTill => $validTill]}
    {else}
        {_"$websiteType.leaflet.metaTitle", [brand => $leaflet->getName(), validSince => $validSince, validTill => $validTill]}
        {if $currentPage > 1} - {_"$websiteType.leaflet.metaTitlePageSuffix", [page => $currentPage]}{/if}
    {/if}
{/block}

{block description}
    {capture $validTillDay}{$leaflet->getValidTill()|dayGenitive}{/capture}
    {capture $leafletPageCount}{count($leaflet->getPages())}{/capture}

    {if $leaflet->isExpired()}
        {_oferto.leaflet.expiredLeafletDescription, [brand => $shop->getName(), validTill => $validTill]}
        {if $validLeaflet}
            {capture $validSinceDay}{$validLeaflet->getValidSince()|dayGenitive}{/capture}
            {capture $validSince}{$validLeaflet->getValidSince()|localDate:'long'}{/capture}
            {_oferto.leaflet.actualLeafletValidSince, [validSince => $validSince, validSinceDay => $validSinceDay]}
        {/if}
    {elseif $leaflet->isFuture()}
        {_oferto.leaflet.futureLeafletDescription, [brand => $shop->getName(), validSince => $validSince]}
    {else}
        {_"$websiteType.leaflet.metaDesc", [brand => $leaflet->getShop()->getName(), leafletPageCount => $leafletPageCount, validSince => $validSince, validSinceDay => $validSinceDay, validTill => $validTill, validTillDay => $validTillDay]|noescape}
    {/if}
{/block}

{block robots}{if $leaflet->isArchived()}noindex,follow{elseif $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block head}
    <!-- oferto.com / sticky -->
    {include "../components/sticky.latte"}    
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_"$websiteType.navbar.leaflets"}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link" n:if="$shop->getTag()">{$shop->getTag()->getName()}</a> |
            <a n:href="Shop:shop $shop" class="link">{$shop->getName()}</a> |

            {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
                {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
            {else}
                <span class="color-grey">{$leaflet->getName()}</span>
            {/if}
        </p>
    </div>
{/block}

{block scripts}
    {include parent}

    {capture $leafletName |stripHtml|spaceless}
        {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
            {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
        {else}
           {$leaflet->getName()}
        {/if}
    {/capture}

    {capture $leafletSchemaName |spaceless}
        {$leafletName} - {_"$websiteType.leaflet.metaTitlePageSuffix", ['page' => $currentPage]}
    {/capture}

<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": {$leafletSchemaName},
        "url":  {$presenter->link('//this')},
        "numberOfItems": {$leaflet->getCountOfPages()},
        "itemListOrder": "Ascending",
        "itemListElement": [
{foreach $leaflet->getPages() as $page}
{
    "@type": "ListItem",
    "position": {$page->getPageNumber()},
    "name": {$leafletName . ' – ' . ("$websiteType.leaflet.metaTitlePageSuffix", ['page' => $page->getPageNumber()] |translate)},
    "description": {_"$websiteType.leaflet.leafletPageDescription", ['validSinceDay' => $validSinceDay, 'validSince' => $validSince, 'brand' => $shop->getName(), 'pageNumber' => $page->getPageNumber()]},
    "url": {$page->getImageUrl() |image:768,null,'fit','webp'}
}{sep},{/sep}
{/foreach}
        ]
    }
</script>

<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": {_oferto.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.leaflets},
                "item": {link //Leaflets:leaflets}
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$shop->getTag()->getName()},
                "item": {link //Tag:tag $shop->getTag()}
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": {$shop->getName()},
                "item": {link //Shop:shop $shop}
            },
            {
                "@type": "ListItem",
                "position": 5,
                "name": {$leafletName},
                "item": {link //this}
            }
]
}
</script>

    {if $leaflet->getOfferistaBrochureId() !== null}
        <script>
            (function() {
                let pageViewTrackUuid = null;

                // Track page view and store trackUuid
                fetch({link :Api:Offerista:brochurePageView}, {
                    method: "POST",
                    headers: {'X-Requested-With': 'XMLHttpRequest'},
                    body: JSON.stringify({
                        brochureId: {$leaflet->getOfferistaBrochureId()},
                        page: {$currentPage},
                        leafletId: {$leaflet->getId()}
                    }),
                })
                    .then(response => response.json())
                    .then(payload => {
                        console.log(payload);
                        pageViewTrackUuid = payload.trackUuid;
                    });

                // Track duration with related trackUuid
                const startTime = Date.now();

                function trackTimeSpent() {
                    const timeSpent = Date.now() - startTime;

                    const payload = {
                        brochureId: {$leaflet->getOfferistaBrochureId()},
                        page: {$currentPage},
                        leafletId: {$leaflet->getId()},
                        duration: timeSpent / 1000,
                        relatedTrackUuid: pageViewTrackUuid
                    };

                    const url = {link :Api:Offerista:brochurePageViewDuration, websiteId: $presenter->website->getId()};
                    const data = new Blob([JSON.stringify(payload)], { type: 'application/json' });

                    if (!navigator.sendBeacon(url, data)) {
                        fetch(url, {
                            method: "POST",
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify(payload),
                            keepalive: true
                        });
                    }
                }

                window.addEventListener('unload', trackTimeSpent);
                window.addEventListener('beforeunload', trackTimeSpent);
            })();
        </script>

        <script>
            const leafletSlug = {$leaflet->getSlug()};
            const brochureId = {$leaflet->getOfferistaBrochureId()};
            const referrer = document.referrer;

            const navEntry = performance.getEntriesByType("navigation")[0];
            const isReload = navEntry && navEntry.type === "reload";

            if (!referrer.includes(leafletSlug) && !isReload) {
                (function() {
                    const trackingBugs = {$leaflet->getOfferistaTrackingBugs()};

                    trackingBugs.forEach(url => {
                        const cacheBuster = Date.now();
                        const finalUrl = url.replace('%%CACHEBUSTER%%', cacheBuster);
                        const img = new Image();
                        img.src = finalUrl;
                    });

                    console.log('Tracking bugs fired for leaflet:', leafletSlug);
                })();

                (function() {
                    fetch({link :Api:Offerista:brochureClick, websiteId: $presenter->website->getId()}, {
                        method: "POST",
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            brochureId: brochureId
                        }),
                    })
                        .then(response => response.json())
                        .then(payload => {
                            console.log('Offerista click tracked');
                        });
                })();
            }
        </script>
    {/if}
{/block}

{block content}
{capture $validTillDay}{$leaflet->getValidTill()|dayGenitive}{/capture}
{capture $leafletPageCount}{count($leaflet->getPages())}{/capture}

{dump "weeklyads-leaflet"}

<div class="leaflet k-lf-layout o-optimize-2">
    <div class="container">
        <div class="leaflet__content">
            <div class="d-block overflow-hidden">

                <div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
                    <div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {_"$websiteType.leaflet.leaflet", [brand => $leaflet->getName()]}
                            <span class="leaflet__date" n:if="$leaflet->isChecked()">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate}</span>
                        </h1>
                    </div>
                </div>

                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    {if $channel == 'c1' && $localization->isCzech()}
                        <!-- In content -->
                        <div id="protag-in_content"></div>
                        <script type="text/javascript">
                        window.googletag = window.googletag || { cmd: [] };
                        window.protag = window.protag || { cmd: [] };
                        window.protag.cmd.push(function () {
                            window.protag.display("protag-in_content");
                        });
                        </script>           
                    {elseif $channel !== 'e1' && $channel !== 'e2' && $channel !== 'e3'}
                        <div class="" n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                            <!-- MrOferto - Responsive - 1-->
                            <ins class="adsbygoogle adslot-2"
                                style="display:block"
                                data-ad-client="ca-pub-3454721603118795"
                                data-ad-slot="9919352231"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>                            
                        </div>                    

                        <div class="" n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                            <!-- MrOferto - Responsive - 1 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="8096953920"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>                        
                        </div>

                        {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                            <!-- MrOferto - Responsive - 1 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-6277661924921144"
                                data-ad-slot="5929296447"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>

                        {/if}

                        {if $website->hasAdSense() && $website->hasStarioAdsense()}
                            <!-- MrOferto - Responsive - 1 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4363508753950122"
                                data-ad-slot="6524392817"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                        {/if}

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>                
                    {/if}
                </div>                

                {if $leaflet->isExpired()}
                    <div class="mb-3 text-center">
                        <h3>{_oferto.leaflet.expiredLeafletHeading}</h3>
                        <div class="d-flex">
                            <a n:href="Shop:shop $leaflet->getShop()" class="link ml-auto k-show-more-button">{_oferto.leaflet.expiredLeafletLinkToShop, [brand => $leaflet->getShop()->getName()]}</a>
                        </div>
                    </div>
                {else}
                    {if $leaflet->isArchived() === false}
                        <div class="k-paginator__wrapper--2">
                            {include 'newPaginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                        </div>
                    {/if}
                {/if}

                <div class="us-wrapper us-wrapper--130">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    {if $channel == 'c1' && $localization->isCzech()}
                        <!-- In content1 -->
                        <div id="protag-in_content_1"></div>
                        <script type="text/javascript">
                        window.googletag = window.googletag || { cmd: [] };
                        window.protag = window.protag || { cmd: [] };
                        window.protag.cmd.push(function () {
                            window.protag.display("protag-in_content_1");
                        });
                        </script>
                    {else}

                        <div n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                            <!-- MrOferto - Responsive - 5 -->
                            <ins class="adsbygoogle adslot-2"
                                style="display:block"
                                data-ad-client="ca-pub-3454721603118795"
                                data-ad-slot="1719922261"                                
                                data-full-width-responsive="true"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>


                        <div n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                            <!-- MrOferto - Responsive - 5 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="9693052724"                                
                                data-full-width-responsive="true"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>

                        {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                            <!-- MrOferto - Responsive - 5 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-6277661924921144"
                                data-ad-slot="4041499701"                                
                                data-full-width-responsive="true"></ins>
                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        {/if}

                        {if $website->hasAdSense() && $website->hasStarioAdsense()}
                            <!-- MrOferto - Responsive - 5 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4363508753950122"
                                data-ad-slot="1929761435"                                
                                data-full-width-responsive="true"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        {/if}
                    {/if}
                </div>

                <div n:if="$isVirtualPage === true">
                    <div class="k-leaflets__wrapper k-leaflets__wrapper--3 mb-5 mx-0">
                        {dump $topLeaflets}
                        {foreach $topLeaflets as $topLeaflet}
                            {continueIf $topLeaflet === $leaflet}

                            {include '../components/leaflet.latte', leaflet => $topLeaflet, validBadgeShow => false}
                        {/foreach}
                    </div>
                </div>

                <div n:if="$isVirtualPage === false" style="position: relative" class="leaflet-preview o-leaflet-preview mb-5">¨
                    {var $leafletPage = $leaflet->getPageByNumber($currentPage)}

                    <picture id="" data-expand="100">
                        <source
                                data-srcset="
                                {$leafletPage->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                {$leafletPage->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                            "
                                type="image/webp"
                        >
                        <img
                                src="{$basePath}/images/placeholder-870.png"
                                data-srcset="
                                {$leafletPage->getImageUrl() |image:768,null} 768w,
                                {$leafletPage->getImageUrl() |image:1740,null} 1740w
                            "
                                data-sizes="auto"
                                width="870"
                                height="1190"
                                alt="{$leaflet->getShop()->getName()}"
                                class="lazyload"
                        >
                    </picture>

                    {if $leafletPage->getClickouts()}
                        {foreach $leafletPage->getClickoutsArray() as $clickout}
                            <div class="kf-click-out" style="position: absolute; z-index: 9999; left: {$clickout['left']*100 |noescape}%; top: {$clickout['top']*100 |noescape}%; min-width: 3%; width: {$clickout['width']*100 |noescape}%; min-height: 3%; height: {$clickout['height']*100 |noescape}%;">
                                <a n:href=":Api:Offerista:clickout, target: $clickout['url'], brochureId: $leaflet->getOfferistaBrochureId(), page: $leafletPage->getPageNumber(), " target="_blank" rel="nofollow" title="">
                                    <svg viewBox="0 0 24 24">
                                        <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"></path>
                                    </svg>
                                </a>
                            </div>
                        {/foreach}
                    {/if}
                </div>

                {*
                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    {if $channel == 'c1' && $localization->isCzech()}
                        <!-- In content2 -->
                        <div id="protag-in_content_2"></div>
                        <script type="text/javascript">
                        window.googletag = window.googletag || { cmd: [] };
                        window.protag = window.protag || { cmd: [] };
                        window.protag.cmd.push(function () {
                            window.protag.display("protag-in_content_2");
                        });
                        </script>

                    {else}
                        <div n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                            <!-- MrOferto - Responsive - 4 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-3454721603118795"
                                data-ad-slot="3541097402"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>

                        <div n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                            <!-- MrOferto - Responsive - 4 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="6783872258"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>

                        {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                            <!-- MrOferto - Responsive - 4 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-6277661924921144"
                                data-ad-slot="9293826384"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        {/if}

                        {if $website->hasAdSense() && $website->hasStarioAdsense()}
                            <!-- MrOferto - Responsive - 4 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4363508753950122"
                                data-ad-slot="5869006448"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>

                                <script>
                                    (adsbygoogle = window.adsbygoogle || []).push({});
                                </script>
                        {/if}
                    {/if}
                </div>
                *}

                {if $leaflet->isArchived() === false}
                    <div class="k-paginator__wrapper--2">
                        {include 'newPaginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                    </div>
                {/if}
                
                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    {if $channel == 'c1' && $localization->isCzech()}
                        <!-- In content3 -->
                        <div id="protag-in_content_3"></div>
                        <script type="text/javascript">
                        window.googletag = window.googletag || { cmd: [] };
                        window.protag = window.protag || { cmd: [] };
                        window.protag.cmd.push(function () {
                            window.protag.display("protag-in_content_3");
                        });
                        </script>

                    {elseif $channel !== 'e1' && $channel !== 'e2' && $channel !== 'e3'}                                                            
                        <div n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                            <!-- MrOferto - Responsive - 2 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-3454721603118795"
                                data-ad-slot="8234472140"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                        </div>
                        

                        <div n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                            <!-- MrOferto - Responsive - 2 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="5842309769"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                        </div>

                        {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                            <!-- MrOferto - Responsive - 2 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-6277661924921144"
                                data-ad-slot="6859234731"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                        {/if}

                        {if $website->hasAdSense() && $website->hasStarioAdsense()}
                            <!-- MrOferto - Responsive - 2 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4363508753950122"
                                data-ad-slot="8244668592"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                        {/if}

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    {/if}
                </div>

                {if $user->isLoggedIn() && $localization->isHungarian() && $currentPage === 1}
                    <div class="overflow-x-auto whitespace-nowrap pb-4">
                        <div class="inline-flex mb-5">
                        {foreach $leaflet->getPages() as $page}
                            {continueIf $iterator->first}

                            <div class="mr-3">
                                <a n:href="leaflet, shop: $shop, leaflet: $leaflet, page: $page->getPageNumber()" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if}">
                                    <picture>
                                        <source srcset="{$page->getImageUrl() |image:100,120,'exactTop','webp'}" type="image/webp">
                                        <img src="{$page->getImageUrl() |image:100,120,'exactTop'}" width="100" height="120" alt="{$leaflet->getName()} - {$page->getPageNumber()}" loading="lazy" class="">
                                    </picture>
                                </a>
                                {*
                                <p class="k-leaflets__date mt-0 mb-0" n:if="$leaflet->isChecked()">
                                    {$page->getPageNumber()}
                                </p>
                                *}
                            </div>
                        {/foreach}
                        </div>
                    </div>
                {/if}

                {capture $leafletBrandLink}
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}

                <div class="px-3 px-lg-0">
                    <p class="page-header__text mw-600">
                        {_"$websiteType.leaflet.metaDesc", [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay, validTill => $validTill, validTillDay => $validTillDay, leafletPageCount => $leafletPageCount]|noescape}
                    </p>                        

                    <p class="color-grey fz-m lh-15 mb-3"><strong>{_"$websiteType.leaflet.smallTitle", [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_"$websiteType.leaflet.desc.1", [leafletBrandLink => $leafletBrandLink, brand => $leaflet->getName(), validSince => $validSince, validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}
                    </p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_"$websiteType.leaflet.desc.2", [leafletBrandLink => $leafletBrandLink, brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay, validTill => $validTill, validTillDay => $validTillDay, leafletPageCount => $leafletPageCount] |noescape}
                    </p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_"$websiteType.leaflet.desc.3"}
                    </p>
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_"$websiteType.leaflet.backToLeaflets"}</a>
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_"$websiteType.leaflet.allBrandLeaflets", [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>
                
            </div>
        </div>

        <div class="leaflet__sidebar" style="height: auto !important;" n:if="count($similarLeaflets) > 0">            
            <div class="lf__box">
                <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_"$websiteType.leaflet.similarLeaflets", [brand => $leaflet->getShop()->getName()]}</h3>
                <div class="lf__box-wrapper">
                    <div n:foreach="$similarLeaflets as $similarLeaflet" class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                        <a class="lf__box-image-wrapper {if $similarLeaflet->isExpired()}expired{/if} lf__box-image--medium mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">
                            <picture>
                                <source data-srcset="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$similarLeaflet->getName()}" class="img-responsive lazyload">
                            </picture>
                        </a>
                        <p class="fz-xxs fz-sm-xs mb-0">
                            <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">{$similarLeaflet->getName()}</a>
                            <small>{$similarLeaflet->getValidSince()|localDate} - {$similarLeaflet->getValidTill()|localDate:'long'}</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="lf__box lf__box-lg-border">
                <h3 class="lf__box-title px-3 px-lg-0">{_"$websiteType.leaflet.recommendedLeaflets"}</h3>
                <div class="lf__box-wrapper">
                    {foreach $recommendedLeaflets as $recommendedLeaflet}
                        {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                        <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                            <a class="lf__box-image-wrapper {if $recommendedLeaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">
                                <picture>
                                    <source data-srcset="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                    <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$recommendedLeaflet->getName()}" class="img-responsive lazyload">
                                </picture>
                            </a>
                            <p class="fz-xxs fz-sm-xs mb-0">
                                <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">{$recommendedLeaflet->getName()}</a>
                                <small>{$recommendedLeaflet->getValidSince()|localDate} - {$recommendedLeaflet->getValidTill()|localDate:'long'}</small>
                            </p>
                        </div>
                    {/foreach}
                </div>
            </div>

            <div class="float-wrapper">       
                {if $channel == 'c1' && $localization->isCzech()}
                    <!-- After content -->
                    <div id="protag-after_content"></div>
                    <script type="text/javascript">
                    window.googletag = window.googletag || { cmd: [] };
                    window.protag = window.protag || { cmd: [] };
                    window.protag.cmd.push(function () {
                        window.protag.display("protag-after-content");
                    });
                    </script>

                    <!-- Sidebar -->
                    <div id="protag-sidebar"></div>
                    <script type="text/javascript">
                    window.googletag = window.googletag || { cmd: [] };
                    window.protag = window.protag || { cmd: [] };
                    window.protag.cmd.push(function () {
                        window.protag.display("protag-sidebar");
                    });
                    </script>


                {else}
                    <!-- oferto.com / halfpage2 -->
                    {include "../components/halfpage1.latte"}                                
                {/if}
            </div>
        </div>
    </div>

    <div class="float-wrapper__stop"></div>

    <div class="container">        
        <div>                        
            <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.leaflet.otherArticles"}</h2>
            {include "../components/article-list.latte", articles => $articles}
        </div>
    </div>
</div>


