{block title}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{if $leaflet->isChecked()}
    {_kaufino.leaflet.metaTitle, [brand => $leaflet->getName(), validSince => $validSince]}
{else}
    {_kaufino.leaflet.metaTitleUnChecked, [brand => $leaflet->getName()]}
{/if}
{/block}

{block description}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{if $leaflet->isChecked()}
    {_kaufino.leaflet.metaDesc, [brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
{else}
    {_kaufino.leaflet.metaDescUnChecked, [brand => $leaflet->getName()]|noescape}
{/if}
{/block}

{block robots}{if $leaflet->isArchived()}noindex,follow{elseif $currentPage > 1 || $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container k-breadcrumb__container--leaflet mt-4">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link">{$shop->getTag()->getName()}</a> |
            <a n:href="Shop:shop $shop" class="link">{$shop->getName()}</a>

            {*
            {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
                {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('w') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
            {else}
                <span class="color-grey">{$leaflet->getName()}</span>
            {/if}
            *}
        </p>
    </div>
{/block}

{block head}
    {include parent}
    <script n:syntax="double">
        window.dataLayer.push({
            'content_group' : 'Leaflet',
            'country' : {{$localization->getRegion()}}
        });
    </script>

    <link
        rel="preload"
        as="image"
        href="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null,'fit','webp'}"
    />

    {*}
    <link
        rel="preload"
        as="image"
        href="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null}"
    />
    *}

    {if $leaflet->getOfferistaBrochureId() !== null}
        <script>
            (function() {
                // Track page view
                fetch({link :Api:Offerista:brochurePageView, websiteId: $presenter->website->getId()}, {
                    method: "POST",
                    headers: {'X-Requested-With': 'XMLHttpRequest'},
                    body: JSON.stringify({
                        brochureId: {$leaflet->getOfferistaBrochureId()},
                        page: {$currentPage},
                        leafletId: {$leaflet->getId()}
                    }),
                })
                    .then(response => response.json())
                    .then(payload => {
                        console.log(payload);
                        // trackUuid is available in payload.trackUuid if needed for duration tracking
                    });
            })();
        </script>
    {/if}
{/block}

{block scripts}
    {include parent}
{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}

{capture $validSinceShort}{$leaflet->getValidSince()|localDate} {/capture}
{capture $validTillShort}{$leaflet->getValidTill()|localDate}{/capture}

{var $cacheKey = 'leaflet-' . $leaflet->getId() . '-page-' . $currentPage}

<!--<div class="leaflet k-lf-layout">
    <div class="container">
    <div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-10">
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Hypermarkety a supermarkety</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Elektro</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Nábytek</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Sport</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Bydlení a zahrada</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Drogerie a kosmetika</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Lékárny a zdraví</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Ostatní</div>
    </div>
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header&#45;&#45;mobile-row">
					<div class="flex items-center gap-[15px]">
            <div class="w-10 h-10">
                <img src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="lidl">
            </div>
            <div class="flex flex-col">
              <h1 class="text-[24px] font-medium leading-[34px]">
                  {if $leaflet->isChecked()}
                  {_kaufino.leaflet.title, [brand => $leaflet->getName(), validSince => $validSinceShort, validTill => $validTillShort]|noescape}
                  {else}
                  {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                  {/if}
              </h1>
                <div class="flex gap-[9px] items-center font-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                        <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>
                    Obchody
                </div>
            </div>
					</div>

					<div n:if="false" class="leaflet__detail-header-side">
						<a n:href="Shop:shop $leaflet->getShop()">
                            <picture>
                                <source
                                    srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','webp'} 2x
                                    "
                                    type="image/webp"
                                >
                                <img
                                    src="{$basePath}/images/placeholder-80x70.png"
                                    srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','png'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','png'} 2x
                                    "
                                    width="80"
                                    height="70"
                                    alt="{$leaflet->getShop()->getName()}"
                                    class="leaflet__detail-header-logo"
                                >
                            </picture>
						</a>
					</div>
				</div>

                <div>
                    <div class="leaflet-preview mb-5">
                    {foreach $leaflet->getPages() as $page}
                        {breakIf $leaflet->isArchived() && $iterator->getCounter() > $leaflet->getCountOfArchivedPages() || $page->getImageUrl() === null}
                        {if $iterator->first}
                            <picture id="p-{$page->getPageNumber()}">
                                <source
                                    srcset="
                                        {$page->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                        {$page->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                                    "
                                    sizes="
                                        (min-width: 1200px) 645px,
                                        100vw
                                    "
                                    type="image/webp"
                                >
                                <img
                                    src="{$basePath}/images/placeholder-870.png"
                                    srcset="
                                        {$page->getImageUrl() |image:768,null} 768w,
                                        {$page->getImageUrl() |image:1740,null} 1740w
                                    "
                                    sizes="
                                        (min-width: 1200px) 645px,
                                        100vw
                                    "
                                    width="870"
                                    height="1190"
                                    alt="{$leaflet->getShop()->getName()}"
                                >
                            </picture>
                        {else}
                            <picture id="p-{$page->getPageNumber()}" data-expand="100" class="{if $iterator->last}leaflet-last-page{/if}">
                                <source
                                    srcset="
                                        {$page->getImageUrl() |image:768,null,'fit','webp'} 1x,
                                        {$page->getImageUrl() |image:1740,null,'fit','webp'} 2x
                                    "
                                    type="image/webp"
                                >
                                <img
                                    src="{$basePath}/images/placeholder-870.png"
                                    srcset="
                                        {$page->getImageUrl() |image:768,null} 1x,
                                        {$page->getImageUrl() |image:1740,null} 2x
                                    "
                                    data-sizes="auto"
                                    width="870"
                                    height="1190"
                                    alt="{$leaflet->getShop()->getName()}"
                                    class=""
                                    loading="lazy"
                                >
                            </picture>
                        {/if}

                        {if $iterator->odd}
                            &lt;!&ndash; Detail letaku - Responsive - 1 &ndash;&gt;
                            <ins
                                class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="4173441893"
                                data-ad-format="auto"
                                data-full-width-responsive="true">
                            </ins>
                        {else}
                            &lt;!&ndash; Detail letaku - Responsive - 3 &ndash;&gt;
                            <ins
                                class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="2477216848"
                                data-ad-format="auto"
                                data-full-width-responsive="true">
                            </ins>
                        {/if}

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    {/foreach}
                </div>


                <div>
                    &lt;!&ndash; Letaky - Detail letaku - Responsive - 2 &ndash;&gt;
                    <ins class="adsbygoogle mrec-xs mrec-sm mrec-md leaderboard-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="4885879417" data-ad-format="auto" data-full-width-responsive="true"></ins>

                    <script>
                        (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>

                <div class="leaflet__ads-wrapper">
                    &lt;!&ndash; Letaky - Detail letaku - Responsive - 3 &ndash;&gt;
                </div>

                <div n:if="count($offers) > 0 && isset($userLoggedIn)" class="mb-5">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offers, [brand => $shop->getName()]}</h2>

                    <div class="k-offers k-offers&#45;&#45;4">
                        {foreach $offers as $offer}
                            {continueIf !$offer->getLeafletPage()}
                            {breakIf $iterator->getCounter() > 4}
                            {include '../components/offer-item.latte', offer => $offer, hideShop => true}
                        {/foreach}
                    </div>
                </div>

                {capture $leafletBrandLink}
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}

                {capture $leafletPageCount}
                    {count($leaflet->getPages())}
                {/capture}

                {*
                <p class="page-header__text ml-0">
                    {if $leaflet->isChecked()}
                        {_kaufino.leaflet.metaDesc, [brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
                    {else}
                        {_kaufino.leaflet.metaDescUnChecked, [brand => $leaflet->getName()]|noescape}
                    {/if}
                </p>
                *}

                <div class="px-3 px-lg-0 mt-3">
                    {*<p class="color-grey fz-m lh-15 mb-3" n:if="$leaflet->isChecked()"><strong>{_kaufino.leaflet.smallTitle, [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>*}
                    <p class="color-grey fz-m lh-15 mb-5">
                        {if $leaflet->isChecked()}
                            {_'kaufino.leaflet.desc', [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}
                        {else}
                            {_'kaufino.leaflet.descUnChecked', [leafletBrandLink => $leafletBrandLink, leafletPageCount => $leafletPageCount] |noescape}
                        {/if}
                    </p>
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_kaufino.leaflet.backToLeaflets}</a>
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_kaufino.leaflet.allBrandLeaflets, [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>
            </div>
        </div>



            {var $similarLeaflets = $getSimilarLeaflets()}
            <div class="leaflet__sidebar" style="height: auto !important;" n:if="$similarLeaflets && count($similarLeaflets) > 0">
                <div class="lf__box">
                    <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_kaufino.leaflet.similarLeaflets, [brand => $leaflet->getShop()->getName()]}</h3>
                    <div class="lf__box-wrapper">
                        {foreach $similarLeaflets as $similarLeaflet}
                            <div class="lf__box-item lf__box-item&#45;&#45;md-100 flex-direction-column flex-direction-lg-row mb-3">
                                {include '../components/leaflet-small.latte', leaflet => $similarLeaflet}
                            </div>
                        {/foreach}
                    </div>
                </div>

                {*
                <div class="float-wrapper">
                    &lt;!&ndash; Detail letaku - Sidebar - 2 &ndash;&gt;
                    <ins class="adsbygoogle mrec-xs mrec-sm skyscraper-md halfpage-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="5041874235" data-ad-format="auto" data-full-width-responsive="true"></ins>

                    <script>
                        (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>
                *}

                <div class="leaflet__aside">
                    <div class="lf__box lf__box-lg-border">
                        <h3 class="lf__box-title px-3 px-lg-0">{_kaufino.leaflet.recommendedLeaflets}</h3>
                        <div class="lf__box-wrapper">
                            {foreach $getRecommendedLeaflets() as $recommendedLeaflet}
                                {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                                <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                                    {include '../components/leaflet-small.latte', leaflet => $recommendedLeaflet}
                                </div>
                            {/foreach}
                        </div>
                    </div>
            </div>
            </div>
    </div>

	<div class="float-wrapper__stop"></div>
</div>-->

<div class="container">
    <div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-10">
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Hypermarkety a supermarkety</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Elektro</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Nábytek</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Sport</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Bydlení a zahrada</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Drogerie a kosmetika</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Lékárny a zdraví</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Ostatní</div>
    </div>

    <div class="md:flex justify-between">
        <div class="max-w-[816px]">
            <div class="flex flex-col md:flex-row md:items-start md:gap-[15px]">
                <div class="flex md:hidden items-center gap-[14px] mb-[29px] mt-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
                        <path d="M14 6.49405L2.0836 6.50178M6.41308 12L0.999999 6.49397L6.40136 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    Zpět na všechny letáky
                </div>

                <div class="md:hidden text-sm leading-[24.5px] text-white bg-black py-1 px-2.5 max-w-[205px] rounded-md mb-2.5">Tento leták již není aktuální</div>

                <img class="hidden md:block w-[60px] h-[60px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

                <div>
                    <div class="text-[24px] font-medium leading-[34px]">Leták Lidl - Spotřební zboží od 25.11. do 01.12.</div>
                    <div class="hidden md:flex gap-[9px] items-center font-light text-sm text-[#646C7C]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                            <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                        </svg>
                        Obchody
                        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                        </svg>
                        Hypermarkety a supermarkety
                        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                        </svg>
                        Spotřební zboží od 25.11. do 01.12.
                    </div>
                </div>
            </div>

            <div class="mb-[77px]">
                <div class="text-center py-[60px]">
                    <nav class="flex justify-center items-center space-x-2">
                        <a href="#" class="flex justify-center items-center w-8 h-8 cursor-not-allowed rounded-md rounded-md border border-[#DFE3E8]" aria-label="Previous page">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M15.1602 7.41L10.5802 12L15.1602 16.59L13.7502 18L7.75016 12L13.7502 6L15.1602 7.41Z" fill="#C4CDD5"/>
                            </svg>
                        </a>

                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium text-primary border border-primary rounded-md hover:text-primary hover:border-primary hover:bg-primary/10" aria-current="page">1</a>

                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[#DFE3E8] hover:text-primary hover:border-primary hover:bg-primary/10">2</a>

                        <span class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[#DFE3E8]">...</span>

                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[#DFE3E8] hover:text-primary hover:border-primary hover:bg-primary/10">9</a>
                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[##DFE3E8] hover:text-primary hover:border-primary hover:bg-primary/10">10</a>

                        <a href="#" class="flex justify-center items-center bg-primary w-8 h-8 rounded-md rounded-md border border-primary" aria-label="Next page">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M8.83984 7.41L13.4198 12L8.83984 16.59L10.2498 18L16.2498 12L10.2498 6L8.83984 7.41Z" fill="white"/>
                            </svg>
                        </a>
                    </nav>
                </div>

                <div>
                    <img id="mainImage" class="w-full" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/qmBncroI0o1-duL0YrMF_O9cEwsNlcUygnNskODkvWY/resize:fit:768:0:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU2LzI1NjIwNS82NzA2ZTRhYTI3OTM1ZmYzLnc5b2s5anlvNTg1Zy5qcGc.webp" alt="leták">
                </div>

                <div class="text-center py-[60px]">
                    <nav class="flex justify-center items-center space-x-2">
                        <a href="#" class="flex justify-center items-center w-8 h-8 cursor-not-allowed rounded-md rounded-md border border-[#DFE3E8]" aria-label="Previous page">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M15.1602 7.41L10.5802 12L15.1602 16.59L13.7502 18L7.75016 12L13.7502 6L15.1602 7.41Z" fill="#C4CDD5"/>
                            </svg>
                        </a>

                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium text-primary border border-primary rounded-md hover:text-primary hover:border-primary hover:bg-primary/10" aria-current="page">1</a>

                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[#DFE3E8] hover:text-primary hover:border-primary hover:bg-primary/10">2</a>

                        <span class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[#DFE3E8]">...</span>

                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[#DFE3E8] hover:text-primary hover:border-primary hover:bg-primary/10">9</a>
                        <a href="#" class="flex justify-center items-center w-8 h-8 text-sm font-medium rounded-md border border-[##DFE3E8] hover:text-primary hover:border-primary hover:bg-primary/10">10</a>

                        <a href="#" class="flex justify-center items-center bg-primary w-8 h-8 rounded-md rounded-md border border-primary" aria-label="Next page">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M8.83984 7.41L13.4198 12L8.83984 16.59L10.2498 18L16.2498 12L10.2498 6L8.83984 7.41Z" fill="white"/>
                            </svg>
                        </a>
                    </nav>
                </div>

                <div class="font-light leading-7 mb-[13px]">
                    Právě se díváte na aktuální leták z obchodu Penny platný od 28.11.2024 do 04.12.2024. V letáku naleznete na 32 stránkách informace o aktuálních slevách. Navštěvujte pravidelně Kaufino a získejte vždy aktuální informace o všech letácích z nabídek českých obchodů.
                </div>

                <div class="flex gap-6 font-light leading-7 mb-[66px]">
                    <a class="underline" href="#">Zpět na výpis všech letáků</a>
                    <a class="underline" href="#">Všechny letáky Penny</a>
                </div>

                <div class="lg:max-w-[916px] relative">
                    <div class="swiper leaflet" style="mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 60%, rgba(0, 0, 0, 0) 101%)">
                        <div class="swiper-wrapper">
                            {foreach range(1, 10) as $i}
                            <div class="swiper-slide" data-img="https://n.klmcdn.com/zoh4eiLi/IMG/7200/qmBncroI0o1-duL0YrMF_O9cEwsNlcUygnNskODkvWY/resize:fit:768:0:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU2LzI1NjIwNS82NzA2ZTRhYTI3OTM1ZmYzLnc5b2s5anlvNTg1Zy5qcGc.webp">
                                <div class="flex flex-col items-center gap-[13px]">
                                    <div>
                                        <img src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/qmBncroI0o1-duL0YrMF_O9cEwsNlcUygnNskODkvWY/resize:fit:768:0:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU2LzI1NjIwNS82NzA2ZTRhYTI3OTM1ZmYzLnc5b2s5anlvNTg1Zy5qcGc.webp" alt="leták">
                                    </div>

                                    <div class="index-indicator flex items-center justify-center text-sm cursor-pointer rounded w-8 h-8 border border-light-2">
                                        {$i}
                                    </div>
                                </div>
                            </div>
                            {/foreach}
                        </div>
                    </div>


                    <div>
                        <div class="swiper-button-prev leaflet"></div>
                        <div class="swiper-button-next leaflet"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="hidden lg:flex flex-col gap-10">
            <div class="min-w-[300px] h-auto pt-3 pb-[21px] rounded-lg border border-[#F4F4F6]">
                <div class="text-center mb-[13px] text-[20px] leading-[39px]">Další oblíbené letáky</div>

                <div class="px-[35px]">
                    <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer mb-3">
                        <div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
                            <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                                <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                                <div class="w-full relative">
                                    <img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                                </div>
                                <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                                    <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                        Otevřít
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                                <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                                <div class="leading-[21px]">
                                    <div class="text-xs md:text-lg font-medium">Lidl</div>
                                    <div class="text-xs font-light">04.04. - 07.04.2024</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-center">
                        <button class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
                            Všechny letáky
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                                <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-light-6 rounded-lg h-[711px] p-2">
                reklama
            </div>
        </div>
    </div>
</div>


<div class="bg-light-6">
    <div class="container pt-[43px] pb-[51px]">
        <div class="hidden md:block">
            <div class="flex justify-between items-center">
                <div class="text-[26px] font-medium leading-[39px] mb-[41px]">Ďalší obchody</div>
                <div class="hidden md:flex gap-[60px]">
                    <div class="swiper-button-prev favorite-shops"></div>
                    <div class="swiper-button-next favorite-shops"></div>
                </div>
            </div>

            <div class="swiper favorite-shops">
                <div class="swiper-wrapper">
                    {for $i = 1; $i <= 8; $i++}
                    <div class="swiper-slide">
                        <div class="hidden md:block">
                            <svg width="141" height="141" viewBox="0 0 141 141" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M128.286 0H12C5.37258 0 0 5.37259 0 12V128.286C0 134.913 5.37259 140.286 12 140.286H128.286C134.913 140.286 140.286 134.913 140.286 128.286V12C140.286 5.37258 134.913 0 128.286 0Z" fill="#CD1414"/>
                                <path d="M131.52 77.3539C131.52 78.3552 131.223 79.3341 130.667 80.1667C130.111 80.9993 129.32 81.6482 128.395 82.0314C127.47 82.4146 126.452 82.5148 125.47 82.3195C124.488 82.1241 123.585 81.6419 122.877 80.9339C122.169 80.2258 121.687 79.3237 121.492 78.3416C121.296 77.3595 121.397 76.3415 121.78 75.4164C122.163 74.4913 122.812 73.7006 123.645 73.1443C124.477 72.5879 125.456 72.291 126.457 72.291C127.8 72.291 129.088 72.8244 130.037 73.7739C130.987 74.7234 131.52 76.0111 131.52 77.3539Z" fill="#FFD200"/>
                                <path d="M19.3011 69.6978H21.1817C23.3197 69.6978 24.6164 68.3764 24.849 66.7283C25.0469 65.308 24.0571 63.7589 22.0429 63.7589H20.2365L19.3011 69.6978ZM114.456 66.2681L118.742 58.2308H128.596L113.749 81.7685H103.677L108.443 75.0526L102.999 58.7208L99.2876 81.7586H91.4087L86.5686 70.8707L84.8166 81.7586H76.9328L80.7237 58.2209H88.8154L93.5071 68.7525L95.2047 58.2209H112.437L114.456 66.2681ZM77.6108 58.2308L73.8198 81.7685H65.9112L61.0711 70.8806L59.3191 81.7685H51.4353L55.2262 58.2308H63.3179L68.0047 68.7624L69.7022 58.2308H77.6108ZM24.3095 57.8745C29.7535 57.8745 33.4504 60.8439 33.4851 65.5752L34.6432 58.2259H52.4944L51.5343 64.1647H42.1311L41.6609 66.9857H50.1881L49.2379 72.8701H40.6711L40.1762 75.795H49.7773L48.8172 81.7338H30.8571L32.911 68.99C31.5302 73.1175 27.764 75.5475 22.8347 75.5475H18.3113L17.3215 81.7338H8.76953L12.6199 57.8398L24.3095 57.8745Z" fill="white"/>
                            </svg>
                        </div>

                        <div class="md:hidden">
                            <svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M75.9973 0H12C5.37258 0 0 5.37258 0 12V75.9973C0 82.6247 5.37258 87.9973 12 87.9973H75.9973C82.6247 87.9973 87.9973 82.6247 87.9973 75.9973V12C87.9973 5.37258 82.6247 0 75.9973 0Z" fill="#CD1414"/>
                                <path d="M82.4991 48.5215C82.4991 49.1496 82.3128 49.7636 81.9638 50.2859C81.6149 50.8081 81.1189 51.2152 80.5386 51.4556C79.9583 51.6959 79.3197 51.7588 78.7037 51.6363C78.0877 51.5137 77.5218 51.2113 77.0776 50.7671C76.6335 50.323 76.331 49.7571 76.2085 49.1411C76.0859 48.525 76.1488 47.8865 76.3892 47.3062C76.6296 46.7259 77.0366 46.2299 77.5589 45.8809C78.0811 45.532 78.6951 45.3457 79.3233 45.3457C80.1655 45.3457 80.9733 45.6803 81.5689 46.2759C82.1645 46.8715 82.4991 47.6792 82.4991 48.5215Z" fill="#FFD200"/>
                                <path d="M12.1071 43.7193H13.2868C14.6279 43.7193 15.4413 42.8904 15.5872 41.8566C15.7113 40.9657 15.0905 39.994 13.827 39.994H12.6939L12.1071 43.7193ZM71.7954 41.5679L74.4838 36.5264H80.6647L71.3515 51.2909H65.034L68.0236 47.0782L64.6087 36.8337L62.2804 51.2847H57.3382L54.3021 44.455L53.2032 51.2847H48.2579L50.6358 36.5202H55.7115L58.6545 43.1263L59.7193 36.5202H70.5288L71.7954 41.5679ZM48.6832 36.5264L46.3052 51.2909H41.3444L38.3083 44.4612L37.2093 51.2909H32.264L34.642 36.5264H39.7177L42.6575 43.1325L43.7223 36.5264H48.6832ZM15.2488 36.3029C18.6636 36.3029 20.9826 38.1655 21.0043 41.1333L21.7308 36.5233H32.9283L32.3261 40.2485H26.4277L26.1328 42.0181H31.4817L30.8857 45.7092H25.5119L25.2015 47.5439H31.224L30.6218 51.2692H19.3559L20.6442 43.2753C19.7781 45.8644 17.4157 47.3887 14.3237 47.3887H11.4863L10.8654 51.2692H5.50098L7.9162 36.2811L15.2488 36.3029Z" fill="white"/>
                            </svg>
                        </div>
                    </div>
                    {/for}
                </div>
            </div>
        </div>

        <div class="hidden md:block w-full h-px bg-light-2 my-10"></div>

        <div class="text-[26px] font-medium leading-[39px] mb-[26px] md:mb-[41px]">Ďalší letáky</div>

        <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
            {for $i = 1; $i <= 11; $i++}
            <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                <div class="p-1.5 md:p-2 bg-white rounded-xl">
                    <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                        <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                        <div class="w-full relative">
                            <img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                        </div>
                        <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                            <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                Otevřít
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                        <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                        <div class="leading-[21px]">
                            <div class="text-xs md:text-lg font-medium">Lidl</div>
                            <div class="text-xs font-light">04.04. - 07.04.2024</div>
                        </div>
                    </div>
                </div>
            </div>
            {/for}
        </div>
    </div>
</div>

<div class="hidden md:block">
    <div class="container pt-[77px] pb-[55px]">
        <div class="flex items-center gap-4 mb-[32px]">
            <img class="w-[47px] h-[47px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
            <div class="text-[24px] font-medium leading-[34px]">Letáky Lidl najdete také v těchto zemích</div>
        </div>
        <div class="flex flex-wrap gap-1.5 md:gap-3">
            {foreach range(1, 17) as $i}
            <div class="transition-transform duration-200 transform hover:scale-[105%] cursor-pointer rounded-lg flex items-center gap-[5px] md:gap-[11px] p-1.5 md:p-3 text-xs md:text-sm leading-[21px] md:leading-[24.5px] font-light bg-light-6">
                <svg class="w-[15px] h-[15px] md:w-[33px] md:h-[33px]" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                    <path d="M10.1086 0.466494C9.29607 0.165029 8.41728 0 7.49987 0C6.58247 0 5.70368 0.165029 4.89119 0.466494L4.23901 7.5L4.89119 14.5335C5.70368 14.835 6.58247 15 7.49987 15C8.41728 15 9.29607 14.835 10.1086 14.5335L10.7607 7.5L10.1086 0.466494Z" fill="#FFDA44"/>
                    <path d="M15.0002 7.50003C15.0002 4.27532 12.9649 1.52622 10.1089 0.466553V14.5336C12.9649 13.4738 15.0002 10.7248 15.0002 7.50003Z" fill="#D80027"/>
                    <path d="M0 7.50003C0 10.7248 2.03531 13.4738 4.89132 14.5336V0.466553C2.03531 1.52622 0 4.27532 0 7.50003Z" fill="black"/>
                </svg>
                Belgium
            </div>
            {/foreach}
        </div>
    </div>
</div>

<script type="module">
    import Swiper from 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.mjs'

    const FavoriteSwiper = new Swiper('.swiper.favorite-shops', {
        direction: 'horizontal',
        slidesPerView: 3.5,
        breakpoints: {
            1024: {
                slidesPerView: 7,
                spaceBetween: 36,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 36,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.favorite-shops',
            prevEl: '.swiper-button-prev.favorite-shops',
        },
    });

    const LeafletSwiper = new Swiper('.swiper.leaflet', {
        direction: 'horizontal',
        slidesPerView: 3.2,
        spaceBetween: 12,
        breakpoints: {
            1024: {
                slidesPerView: 8.3,
                spaceBetween: 12,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 12,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.leaflet',
            prevEl: '.swiper-button-prev.leaflet',
        },
    });
</script>

<script>
    // LEAFLET SWIPER SCRIPT FOR CHANGING MAIN IMAGE
    document.addEventListener('DOMContentLoaded', () => {
        const mainImage = document.getElementById('mainImage');
        const swiperSlides = document.querySelectorAll('.swiper-slide');

        if (swiperSlides.length > 0) {
            const firstSlide = swiperSlides[0];
            const firstImg = firstSlide.getAttribute('data-img');
            mainImage.setAttribute('src', firstImg);

            const firstIndicator = firstSlide.querySelector('.index-indicator');
            firstIndicator.classList.add('text-primary', 'border-primary');
            firstIndicator.classList.remove('border-light-2');
        }

        swiperSlides.forEach(slide => {
            slide.addEventListener('click', () => {
                const newImg = slide.getAttribute('data-img');
                mainImage.setAttribute('src', newImg);

                document.querySelectorAll('.index-indicator').forEach(indicator => {
                    indicator.classList.remove('text-primary', 'border-primary');
                    indicator.classList.add('border-light-2');
                });

                const indicator = slide.querySelector('.index-indicator');
                indicator.classList.remove('border-light-2');
                indicator.classList.add('text-primary', 'border-primary');

                window.scrollTo({
                    top: window.innerWidth >= 768 ? 180 : 0,
                    behavior: 'smooth'
                });
            });
        });

        document.querySelectorAll('.index-indicator').forEach(indicator => {
            indicator.addEventListener('click', (e) => {
                const slide = indicator.closest('.swiper-slide');
                slide.click();
            });
        });
    });
</script>

<style>
    .swiper.favorite-shops,
    .swiper-button-prev.favorite-shops,
    .swiper-button-next.favorite-shops {
        position: relative;
        color: #292D32;
    }

    .swiper-button-prev.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }
    .swiper-button-next.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }

    /* LEAFLET SWIPER */
    .swiper-button-prev.leaflet,
    .swiper-button-next.leaflet {
        color: white;
        background: black;
        border-radius: 4px;
        width: 32px;
        height: 32px;
        top: 44%;
        right: -30px;
    }
    .swiper-button-prev.leaflet {
        left: -13px;
    }


    .swiper-button-prev.leaflet::after,
    .swiper-button-next.leaflet::after {
        font-size: 14px;
        font-weight: 700;
    }

    .swiper-button-prev.leaflet.swiper-button-disabled,
    .swiper-button-next.leaflet.swiper-button-disabled {
        display: none;
    }

    @media (max-width: 768px) {
        .swiper-button-prev.leaflet,
        .swiper-button-next.leaflet {
            display: none;
        }
    }
</style>

