{block title}
    {capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
    {capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
    {capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
    {if $leaflet->isExpired()}
        {_kaufino.leaflet.expiredLeafletTitle, [brand => $shop->getName(), validTill => $validTill]}
    {elseif $leaflet->isFuture()}
        {_kaufino.leaflet.futureLeafletTitle, [brand => $shop->getName(), validSince => $validSince, validTill => $validTill]}
    {else}
        {if $leaflet->isChecked()}
            {_kaufino.leaflet.metaTitle, [brand => $shop->getName(), validSince => $validSince, validTill => $validTill]}
        {else}
            {_kaufino.leaflet.metaTitleUnChecked, [brand => $shop->getName()]}
        {/if}
    {/if}
{/block}

{block description}
    {capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
    {capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
    {capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
    {if $leaflet->isExpired()}
        {_kaufino.leaflet.expiredLeafletDescription, [brand => $shop->getName(), validTill => $validTill]}
        {if $validLeaflet}
            {capture $validSinceDay}{$validLeaflet->getValidSince()|dayGenitive}{/capture}
            {capture $validSince}{$validLeaflet->getValidSince()|localDate:'long'}{/capture}
            {_kaufino.leaflet.actualLeafletValidSince, [validSince => $validSince, validSinceDay => $validSinceDay]}
        {/if}
    {elseif $leaflet->isFuture()}
        {_kaufino.leaflet.futureLeafletDescription, [brand => $shop->getName(), validSince => $validSince]}
    {else}
        {if $leaflet->isChecked()}
            {_kaufino.leaflet.metaDesc, [brand => $shop->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
        {else}
            {_kaufino.leaflet.metaDescUnChecked, [brand => $shop->getName()]|noescape}
        {/if}
    {/if}
{/block}

{block robots}{if $leaflet->isArchived()}noindex,follow{elseif $currentPage > 1 || $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container k-breadcrumb__container--leaflet">
        <p class="k-breadcrumb">            
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
            <a n:href="Tag:tag $shop->getTag()" n:if="$shop->getTag()" class="link">{$shop->getTag()->getName()}</a> |
            <a n:href="Shop:shop $shop" class="link">{$shop->getName()}</a>

            {*
            {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
                {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('w') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
            {else}
                <span class="color-grey">{$leaflet->getName()}</span>
            {/if}
            *}
        </p>
    </div>
{/block}

{block head}
    {include parent}
    <script n:syntax="double">
        window.dataLayer.push({
            'content_group' : 'Leaflet',
            'country' : {{$localization->getRegion()}}
        });
    </script>

    <link
            n:if="$isVirtualPage === false"
            rel="preload"
            as="image"
            href="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null,'fit','webp'}"
    />

    {if ($channel === 'l')}
        <script>
            // Definování Out-of-page slotů (sticky a interstitial)
            googletag.cmd.push(function() {
                googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_popup_testgroup1B', 'sticky').addService(googletag.pubads());
                googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_interstitial_testgroup1B', 'interstitial').addService(googletag.pubads());
                googletag.enableServices();
            });
        </script>
    {elseif ($channel === 'm')}
        <script>
            // Definování Out-of-page slotů (sticky a interstitial)
            googletag.cmd.push(function() {
                googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_popup_testgroup1C', 'sticky').addService(googletag.pubads());
                googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_interstitial_testgroup1C', 'interstitial').addService(googletag.pubads());
                googletag.enableServices();
            });
        </script>
    {elseif ($channel === 'n')}
        <script async src="https://cdn.performax.cz/yi/adsbypx/px_autoads.js?aab=ulite"></script>
        <link rel="stylesheet" href="https://cdn.performax.cz/yi/adsbypx/px_autoads.css"/>
    {/if}
{/block}

{block scripts}
    {include parent}

    {capture $validSinceShort}{$leaflet->getValidSince()|localDate} {/capture}
    {capture $validTillShort}{$leaflet->getValidTill()|localDate}{/capture}

    {capture $leafletName |stripHtml|spaceless}
        {if $leaflet->isChecked()}
            {capture $shopBrandName}{$leaflet->getName()|upper}{/capture}
            {_kaufino.leaflet.title, [brand => $shopBrandName, validSince => $validSinceShort, validTill => $validTillShort]|noescape}
        {else}
            {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
        {/if}
    {/capture}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": {_kaufino.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.leaflets},
                "item": {link //Leaflets:leaflets}
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$shop->getTag()?->getName()},
                "item": {link //Tag:tag $shop->getTag()}
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": {$shop->getName()},
                "item": {link //Shop:shop $shop}
            },
            {
                "@type": "ListItem",
                "position": 5,
                "name": {$leafletName},
                "item": {link //this}
        }
]
}
    </script>

    {if $leaflet->getOfferistaBrochureId() !== null}
        <script>
            (function() {
                let pageViewTrackUuid = null;

                // Track page view and store trackUuid
                fetch({link :Api:Offerista:brochurePageView, websiteId: $presenter->website->getId()}, {
                    method: "POST",
                    headers: {'X-Requested-With': 'XMLHttpRequest'},
                    body: JSON.stringify({
                        brochureId: {$leaflet->getOfferistaBrochureId()},
                        page: {$currentPage},
                        leafletId: {$leaflet->getId()}
                    }),
                })
                    .then(response => response.json())
                    .then(payload => {
                        console.log(payload);
                        pageViewTrackUuid = payload.trackUuid;
                    });

                // Track duration with related trackUuid
                const startTime = Date.now();

                function trackTimeSpent() {
                    const timeSpent = Date.now() - startTime;

                    const payload = {
                        brochureId: {$leaflet->getOfferistaBrochureId()},
                        page: {$currentPage},
                        leafletId: {$leaflet->getId()},
                        duration: timeSpent / 1000,
                        relatedTrackUuid: pageViewTrackUuid
                    };

                    const url = {link :Api:Offerista:brochurePageViewDuration, websiteId: $presenter->website->getId()};
                    const data = new Blob([JSON.stringify(payload)], { type: 'application/json' });

                    if (!navigator.sendBeacon(url, data)) {
                        fetch(url, {
                            method: "POST",
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify(payload),
                            keepalive: true
                        });
                    }
                }

                window.addEventListener('unload', trackTimeSpent);
                window.addEventListener('beforeunload', trackTimeSpent);
            })();
        </script>

        <script>
            const leafletSlug = {$leaflet->getSlug()};
            const brochureId = {$leaflet->getOfferistaBrochureId()};
            const referrer = document.referrer;

            const navEntry = performance.getEntriesByType("navigation")[0];
            const isReload = navEntry && navEntry.type === "reload";

            if (!referrer.includes(leafletSlug) && !isReload) {
                (function() {
                    const trackingBugs = {$leaflet->getOfferistaTrackingBugs()};

                    trackingBugs.forEach(url => {
                        const cacheBuster = Date.now();
                        const finalUrl = url.replace('%%CACHEBUSTER%%', cacheBuster);
                        const img = new Image();
                        img.src = finalUrl;
                    });

                    console.log('Tracking bugs fired for leaflet:', leafletSlug);
                })();

                (function() {
                    fetch({link :Api:Offerista:brochureClick, websiteId: $presenter->website->getId()}, {
                        method: "POST",
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            brochureId: brochureId
                        }),
                    })
                        .then(response => response.json())
                        .then(payload => {
                            console.log('Offerista click tracked');
                        });
                })();
            }
        </script>
    {/if}
{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}

{capture $validSinceShort}{$leaflet->getValidSince()|localDate} {/capture}
{capture $validTillShort}{$leaflet->getValidTill()|localDate}{/capture}

{if ($channel === 'l' || $channel === 'm')}
    <!-- Slot pro Sticky formáty -->
    <div id='sticky' style="height:0">
        <script>
            googletag.cmd.push(function() { googletag.display('sticky'); });
        </script>
    </div>

    <!-- Block pro formát Interstitial -->
    <div id='interstitial' style="height:0">
        <script>
            googletag.cmd.push(function() { googletag.display('interstitial'); });
        </script>
    </div>
{/if}

{include adUnit, 'leaflet_ad_1'}
{include adUnit, 'leaflet_ad_1_mob', 'mobile'}

<div class="leaflet k-lf-layout">
    <div class="container">
        <div class="leaflet__content">
            <div class="d-block mt-3 flex-grow-1">

                {include adUnit, 'leaflet_ad_2'}
                {include adUnit, 'leaflet_ad_2_mob', 'mobile'}

                <div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row k-subdomain">
                        <div class="leaflet__detail-header-content">
                            <h1 class="page-header__title">
                                {if $leaflet->isChecked()}
                                    {capture $shopBrandName}{$leaflet->getName()|upper}{/capture}
                                    {_kaufino.leaflet.title, [brand => $shopBrandName, validSince => $validSinceShort, validTill => $validTillShort]|noescape}
                                {else}
                                    {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                                {/if}
                            </h1>
                        </div>
				</div>

                {if $channel === 'k'}
                    <div class="ads-container">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- Kaufino - Responsive - 1 -->
                        <ins class="adsbygoogle"
                             style="display:block"
                             data-ad-client="ca-pub-4233432057183172"
                             data-ad-slot="2105301778"
                             data-ad-format="auto"
                             data-full-width-responsive="true"></ins>

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    </div>
                {elseif ($channel === 'l' || $channel === 'm')}
                {elseif ($channel === 'n')}
                {else}
                    {if false && $localization->isSlovak()}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- /23037269705/kaufino/shopdetail_1 -->
                            <div id='div-gpt-ad-1718185759126-0' style='min-width: 250px; min-height: 250px;'>
                                <script>
                                    googletag.cmd.push(function() { googletag.display('div-gpt-ad-1718185759126-0'); });
                                </script>
                            </div>
                        </div>
                    {else}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- Kaufino - Responsive - 1 -->
                            <ins class="adsbygoogle"
                                 style="display:block"
                                 data-ad-client="ca-pub-4233432057183172"
                                 data-ad-slot="2105301778"
                                 data-ad-format="auto"
                                 data-full-width-responsive="true"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>
                    {/if}
                {/if}

                {if $leaflet->isArchived() === false}
                    <div class="k-paginator__wrapper--2">
                        {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                    </div>
                {/if}

                {if false && $channel === 'k'}
                    <div class="ads-container">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- Kaufino - Responsive - 5 -->
                        <ins class="adsbygoogle"
                             style="display:block"
                             data-ad-client="ca-pub-4233432057183172"
                             data-ad-slot="3534666039"
                             data-ad-format="auto"
                             data-full-width-responsive="true"></ins>

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    </div>
                {elseif ($channel === 'l' || $channel === 'm')}
                {include adUnit, 'leaflet_ad_3'}
                {include adUnit, 'leaflet_ad_3_mob', 'mobile'}
                {elseif $channel === 'n'}
                    <div class="ads-container">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- letaky.kaufino.com / medium-rectangle2-direct -->
                        <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                        <div id="PX_35682_587540144234504"></div>
                        <script>
                            window.px2 = window.px2 || { conf: {},queue: [] };
                            px2.queue.push(function () {
                                px2.render({
                                    slot: {
                                        id: 35682
                                    },
                                    elem: "PX_35682_587540144234504"
                                })
                            });
                        </script>

                        <!-- letaky.kaufino.com / mobile-rectangle2-direct -->
                        <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                        <div id="PX_35667_493690343150761"></div>
                        <script>
                            window.px2 = window.px2 || { conf: {},queue: [] };
                            px2.queue.push(function () {
                                px2.render({
                                    slot: {
                                        id: 35667
                                    },
                                    elem: "PX_35667_493690343150761"
                                })
                            });
                        </script>
                    </div>
                {else}
                    {if false && $localization->isSlovak()}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- /23037269705/kaufino/shopdetail_2 -->
                            <div id='div-gpt-ad-1718185863660-0' style='min-width: 250px; min-height: 250px;'>
                                <script>
                                    googletag.cmd.push(function() { googletag.display('div-gpt-ad-1718185863660-0'); });
                                </script>
                            </div>
                        </div>
                    {else}
                        <div class="us-wrapper us-wrapper--130">
                            <div>
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- Kaufino - Responsive - 5 -->
                                <ins class="adsbygoogle"
                                    style="display:block; height: 100px;"
                                    data-ad-client="ca-pub-4233432057183172"
                                    data-ad-slot="3534666039"                                    
                                    data-full-width-responsive="true"></ins>

                                <script>
                                    (adsbygoogle = window.adsbygoogle || []).push({});
                                </script>
                            </div>
                        </div>
                    {/if}
                {/if}

                <div n:if="$isVirtualPage === true">
                    <div class="k-leaflets__wrapper k-leaflets__wrapper--3 mb-5 mx-0">
                        {foreach $topLeaflets as $topLeaflet}
                            {continueIf $topLeaflet === $leaflet}

                            {include '../components/leaflet.latte', leaflet => $topLeaflet, validBadgeShow => false}
                        {/foreach}
                    </div>
                </div>

                <div n:if="$isVirtualPage === false">
                    <div class="leaflet-preview mb-5">
                        <div class="leaflet-circle-wrapper">
                            {var $countOfPages = $leaflet->getCountOfPages()}

                            {if $currentPage === $leaflet->getCountOfPages()}
                                {var $countOfPages = $countOfPages + 1}
                                {var $nextPageIsPromoPage = true}
                            {/if}

                            <a n:if="$currentPage > 1" n:href="leaflet shop => $shop, leaflet => $leaflet, page => $currentPage-1" class="leaflet-circle">
                                <svg version="1.1" id="Capa_1" fill="currentColor" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 48 48" style="enable-background:new 0 0 48 48;" xml:space="preserve">
                                    <path class="st0" d="M31,43L13.4,25.4c-0.2-0.2-0.4-0.4-0.4-0.6c-0.1-0.2-0.2-0.5-0.2-0.8s0.1-0.5,0.2-0.8c0.1-0.2,0.2-0.4,0.4-0.6
                                    L31,5.1c0.5-0.5,1.1-0.7,1.7-0.7c0.7,0,1.3,0.2,1.7,0.7c0.5,0.5,0.8,1,0.8,1.8s-0.2,1.3-0.8,1.8L19,24l15.4,15.4
                                    c0.5,0.5,0.8,1.1,0.8,1.8s-0.2,1.3-0.7,1.7c-0.5,0.5-1.1,0.8-1.8,0.8C32,43.7,31.4,43.4,31,43z"/>
                                </svg>
                            </a>
                            
                            {* ADD next page *}
                            <a n:if="$currentPage < $countOfPages" n:href="leaflet shop => $shop, leaflet => $leaflet, page => $currentPage+1" {if $openRelatedLeafletAllowed && $nextPageIsPromoPage}onclick="window.open({link openRelatedLeaflet!, leafletId: $leaflet->getId()}, '_blank');"{/if} class="leaflet-circle leaflet-circle__right">
                                <svg version="1.1" id="Capa_1" fill="currentColor" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 48 48" style="enable-background:new 0 0 48 48;" xml:space="preserve">
                                    <path class="st0" d="M31,43L13.4,25.4c-0.2-0.2-0.4-0.4-0.4-0.6c-0.1-0.2-0.2-0.5-0.2-0.8s0.1-0.5,0.2-0.8c0.1-0.2,0.2-0.4,0.4-0.6
                                    L31,5.1c0.5-0.5,1.1-0.7,1.7-0.7c0.7,0,1.3,0.2,1.7,0.7c0.5,0.5,0.8,1,0.8,1.8s-0.2,1.3-0.8,1.8L19,24l15.4,15.4
                                    c0.5,0.5,0.8,1.1,0.8,1.8s-0.2,1.3-0.7,1.7c-0.5,0.5-1.1,0.8-1.8,0.8C32,43.7,31.4,43.4,31,43z"/>
                                </svg>
                            </a>
                        </div>

                        {var $leafletPage = $leaflet->getPageByNumber($currentPage)}
                        <picture id="p-{$leafletPage->getPageNumber()}">
                            <source
                                    srcset="
                                        {$leafletPage->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                        {$leafletPage->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                                    "
                                    sizes="
                                        (min-width: 1200px) 645px,
                                        100vw
                                    "
                                    type="image/webp"
                            >
                            <img
                                    src="{$basePath}/images/placeholder-870.png"
                                    srcset="
                                        {$leafletPage->getImageUrl() |image:768,null} 768w,
                                        {$leafletPage->getImageUrl() |image:1740,null} 1740w
                                    "
                                    sizes="
                                        (min-width: 1200px) 645px,
                                        100vw
                                    "
                                    width="870"
                                    height="1190"
                                    alt="{$leaflet->getShop()->getName()}"
                                    style="color: transparent; background-size: cover; background-position: 50% 50%; background-repeat: no-repeat; background-image: url(&quot;data:image/svg+xml;base64,Cjxzdmcgd2lkdGg9Ijg1MCIgaGVpZ2h0PSIxMDUwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogIDxkZWZzPgogICAgPGxpbmVhckdyYWRpZW50IGlkPSJnIj4KICAgICAgPHN0b3Agc3RvcC1jb2xvcj0iI2NjY2NjYyIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgIDxzdG9wIHN0b3AtY29sb3I9IiNkOWQ5ZDkiIG9mZnNldD0iNTAlIiAvPgogICAgICA8c3RvcCBzdG9wLWNvbG9yPSIjY2NjY2NjIiBvZmZzZXQ9IjcwJSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSI4NTAiIGhlaWdodD0iMTA1MCIgZmlsbD0iI2NjY2NjYyIgLz4KICA8cmVjdCBpZD0iciIgd2lkdGg9Ijg1MCIgaGVpZ2h0PSIxMDUwIiBmaWxsPSJ1cmwoI2cpIiAvPgogIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItODUwIiB0bz0iODUwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+Cjwvc3ZnPg==&quot;);"
                            >
                        </picture>

                        {if $leafletPage->getClickouts()}
                            {foreach $leafletPage->getClickoutsArray() as $clickout}
                                <div class="kf-click-out" style="position: absolute; z-index: 9999; left: {$clickout['left']*100 |noescape}%; top: {$clickout['top']*100 |noescape}%; min-width: 3%; width: {$clickout['width']*100 |noescape}%; min-height: 3%; height: {$clickout['height']*100 |noescape}%;">
                                    <a n:href=":Api:Offerista:clickout, target: $clickout['url'], brochureId: $leaflet->getOfferistaBrochureId(), page: $leafletPage->getPageNumber(), " target="_blank" rel="nofollow" title="">
                                        <svg viewBox="0 0 24 24">
                                            <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"></path>
                                        </svg>
                                    </a>
                                </div>
                            {/foreach}
                        {/if}
                    </div>

                    {if $channel === 'k'}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- Kaufino - Responsive - 4 -->
                            <ins class="adsbygoogle"
                                 style="display:block"
                                 data-ad-client="ca-pub-4233432057183172"
                                 data-ad-slot="1100074380"
                                 data-ad-format="auto"
                                 data-full-width-responsive="true"></ins>
                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>
                    {elseif ($channel === 'l' || $channel === 'm')}
                    {include adUnit, 'leaflet_ad_4'}
                    {include adUnit, 'leaflet_ad_4_mob', 'mobile'}
                    {elseif $channel === 'n'}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- letaky.kaufino.com / medium-rectangle3-direct -->
                            <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                            <div id="PX_35676_557218344260733"></div>
                            <script>
                                window.px2 = window.px2 || { conf: {},queue: [] };
                                px2.queue.push(function () {
                                    px2.render({
                                        slot: {
                                            id: 35676
                                        },
                                        elem: "PX_35676_557218344260733"
                                    })
                                });
                            </script>

                            <!-- letaky.kaufino.com / mobile-rectangle3-direct -->
                            <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                            <div id="PX_35697_425758749948387"></div>
                            <script>
                                window.px2 = window.px2 || { conf: {},queue: [] };
                                px2.queue.push(function () {
                                    px2.render({
                                        slot: {
                                            id: 35697
                                        },
                                        elem: "PX_35697_425758749948387"
                                    })
                                });
                            </script>
                        </div>
                    {else}
                        {if false && $localization->isSlovak()}
                            <div class="ads-container">
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- /23037269705/kaufino/shopdetail_3 -->
                                <div id='div-gpt-ad-1718185917991-0' style='min-width: 250px; min-height: 250px;'>
                                    <script>
                                        googletag.cmd.push(function() { googletag.display('div-gpt-ad-1718185917991-0'); });
                                    </script>
                                </div>
                            </div>
                        {else}
                            <div class="us-wrapper us-wrapper--130">
                                <div>
                                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                    <!-- Kaufino - Responsive - 4 -->
                                    <ins class="adsbygoogle"
                                        style="display:block"
                                        data-ad-client="ca-pub-4233432057183172"
                                        data-ad-slot="1100074380"
                                        data-ad-format="auto"
                                        data-full-width-responsive="true"></ins>
                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            </div>
                        {/if}
                    {/if}

                    {if $leaflet->isArchived() === false}
                        <div class="k-paginator__wrapper--2">
                            {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                        </div>
                    {/if}

                    {if $channel === 'k'}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- Kaufino - Responsive - 2 -->
                            <ins class="adsbygoogle"
                                 style="display:block"
                                 data-ad-client="ca-pub-4233432057183172"
                                 data-ad-slot="7166056766"
                                 data-ad-format="auto"
                                 data-full-width-responsive="true"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>
                    {elseif ($channel === 'l' || $channel === 'm')}
                    {elseif ($channel === 'n')}
                    {else}
                        {if false && $localization->isSlovak()}
                            <div class="ads-container">
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- /23037269705/kaufino/shopdetail_4 -->
                                <div id='div-gpt-ad-1718185964987-0' style='min-width: 250px; min-height: 250px;'>
                                    <script>
                                        googletag.cmd.push(function() { googletag.display('div-gpt-ad-1718185964987-0'); });
                                    </script>
                                </div>
                            </div>
                        {else}
                            <div class="ads-container">
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- Kaufino - Responsive - 2 -->
                                <ins class="adsbygoogle"
                                     style="display:block"
                                     data-ad-client="ca-pub-4233432057183172"
                                     data-ad-slot="7166056766"
                                     data-ad-format="auto"
                                     data-full-width-responsive="true"></ins>

                                <script>
                                    (adsbygoogle = window.adsbygoogle || []).push({});
                                </script>
                            </div>
                        {/if}
                    {/if}

                {capture $leafletBrandLink}
                        <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                    {/capture}

                {capture $leafletPageCount}
                        {count($leaflet->getPages())}
                    {/capture}

                {*
                <p class="page-header__text ml-0">
                    {if $leaflet->isChecked()}
                        {_kaufino.leaflet.metaDesc, [brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
                    {else}
                        {_kaufino.leaflet.metaDescUnChecked, [brand => $leaflet->getName()]|noescape}
                    {/if}
                </p>
                *}

                    <div class="px-3 px-lg-0 mt-3">
                        {*<p class="color-grey fz-m lh-15 mb-3" n:if="$leaflet->isChecked()"><strong>{_kaufino.leaflet.smallTitle, [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>*}
                        <p class="color-grey fz-m lh-15 mb-5">
                            {if $leaflet->isChecked()}
                                {_'kaufino.leaflet.desc', [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}
                            {else}
                                {_'kaufino.leaflet.descUnChecked', [leafletBrandLink => $leafletBrandLink, leafletPageCount => $leafletPageCount] |noescape}
                            {/if}
                        </p>
                    </div>

                    <div class="d-flex mb-5 px-3 px-lg-0">
                        <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_kaufino.leaflet.backToLeaflets}</a>
                        <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_kaufino.leaflet.allBrandLeaflets, [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>
                </div>
            </div>



            {var $similarLeaflets = $getSimilarLeaflets()}
            <div class="leaflet__sidebar" style="height: auto !important;">
                <div n:if="false" class="lf__box">
                    <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_kaufino.leaflet.similarLeaflets, [brand => $leaflet->getShop()->getName()]}</h3>
                    <div class="lf__box-wrapper">
                        {foreach $similarLeaflets as $similarLeaflet}
                            <div class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                                {include '../components/leaflet-small.latte', leaflet => $similarLeaflet}
                            </div>
                        {/foreach}
                    </div>
                </div>

                <div class="leaflet__aside">
                    <div n:if="$similarLeaflets && count($similarLeaflets) > 0" class="lf__box lf__box-lg-border">
                        <h3 class="lf__box-title px-3 px-lg-0">{_kaufino.leaflet.recommendedLeaflets}</h3>
                        <div class="lf__box-wrapper">
                            {foreach $getRecommendedLeaflets() as $recommendedLeaflet}
                                {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                                <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                                    {include '../components/leaflet-small.latte', leaflet => $recommendedLeaflet}
                                </div>
                            {/foreach}
                        </div>
                    </div>

                    <div class="float-wrapper">
                        {if $channel === 'k'}
                            <div class="ads-container">
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- Kaufino - Responsive - 3 -->
                                <ins class="adsbygoogle"
                                     style="display:block"
                                     data-ad-client="ca-pub-4233432057183172"
                                     data-ad-slot="6974485073"
                                     data-ad-format="auto"
                                     data-full-width-responsive="true"></ins>

                                <script>
                                    (adsbygoogle = window.adsbygoogle || []).push({});
                                </script>
                            </div>
                        {elseif ($channel === 'l' || $channel === 'm')}
                        {include adUnit, 'leaflet_ad_5'}
                        {include adUnit, 'leaflet_ad_5_mob', 'mobile'}
                        {elseif $channel === 'n'}
                            <div class="ads-container">
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- letaky.kaufino.com / halfpage1-direct -->
                                <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                                <div id="PX_35679_970489563757369"></div>
                                <script>
                                    window.px2 = window.px2 || { conf: {},queue: [] };
                                    px2.queue.push(function () {
                                        px2.render({
                                            slot: {
                                                id: 35679
                                            },
                                            elem: "PX_35679_970489563757369"
                                        })
                                    });
                                </script>

                                <!-- letaky.kaufino.com / mobile-rectangle4-direct -->
                                <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                                <div id="PX_35673_198214583421022"></div>
                                <script>
                                    window.px2 = window.px2 || { conf: {},queue: [] };
                                    px2.queue.push(function () {
                                        px2.render({
                                            slot: {
                                                id: 35673
                                            },
                                            elem: "PX_35673_198214583421022"
                                        })
                                    });
                                </script>
                            </div>
                        {else}
                            {if false && $localization->isSlovak()}
                            {else}
                                <div class="ads-container" {*style="background-color: red"*}>
                                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                    <!-- Kaufino - Responsive - 3 -->
                                    <ins class="adsbygoogle"
                                         style="display:block"
                                         data-ad-client="ca-pub-4233432057183172"
                                         data-ad-slot="6974485073"
                                         data-ad-format="auto"
                                         data-full-width-responsive="true"></ins>

                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            {/if}
                        {/if}
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="float-wrapper__stop"></div>

