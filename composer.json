{"name": "kaufino/web", "description": "<PERSON><PERSON><PERSON>", "homepage": "https://kaufino.com", "type": "project", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "require": {"php": "v7.4.24 || ^8.1", "nette/application": "^3.1", "nette/bootstrap": "^3.1", "nette/caching": "^3.1", "nette/database": "^3.1", "nette/di": "^3.0", "nette/finder": "^2.5", "nette/forms": "^3.1", "nette/http": "^3.1", "nette/mail": "^3.1", "nette/robot-loader": "^3.0", "nette/safe-stream": "^2.4", "nette/security": "^3.0", "nette/utils": "^3.1", "latte/latte": "^2.5", "tracy/tracy": "^2.6", "nettrine/orm": "^0.8.0", "nettrine/dbal": "^0.8.0", "nettrine/annotations": "^0.7.0", "nettrine/cache": "^0.3.0", "contributte/console": "v0.9.2", "contributte/translation": "^0.9.0", "dg/adminer-custom": "^1.8", "nextras/static-router": "^2.0", "kdyby/autowired": "^2.1", "ublaboo/datagrid": "^6.0", "sentry/sdk": "^3.1", "contributte/event-dispatcher": "^0.9.0", "contributte/event-dispatcher-extra": "^0.10.0", "psr/log": "v1.1.4", "slevomat/coding-standard": "^8.0", "beberlei/doctrineextensions": "^1.3", "nettrine/migrations": "^0.9.1", "guzzlehttp/psr7": "^2.7.0", "guzzlehttp/guzzle": "^7.8"}, "require-dev": {"codeception/codeception": "5.0", "codeception/module-phpbrowser": "^3.0.1", "codeception/module-asserts": "^3.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-nette": "^1.2", "phpstan/phpstan-doctrine": "^1.3", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"Kaufino\\": "app"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"phpstan": "phpstan analyse --configuration phpstan.neon app bin www", "check": "vendor/bin/phpcs --standard=ruleset.xml --extensions=php --tab-width=4 --colors app bin www", "fix": "vendor/bin/phpcbf --standard=ruleset.xml --extensions=php --tab-width=4 app bin www"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}}}